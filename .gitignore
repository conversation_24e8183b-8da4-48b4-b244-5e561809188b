# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
*.o
*.a

# Go build cache
*.log
*.tmp
*.swp
*.swo
*.DS_Store

# Build output
/bin/
/dist/
/build/
/coverage/
/*.prof

# Dependency directories
vendor/

# IDE/editor files
.idea/
.vscode/
*.iml

# OS generated
Thumbs.db
.DS_Store

# Env files
.env
.env.local
.env.*
!.env.example

# PM2 logs
.pm2/logs/
*.pid
*.pid.lock

# Protobuf generated if re-gen often
proto/*.pb.go

# Module cache
go.sum
