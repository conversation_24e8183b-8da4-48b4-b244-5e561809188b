syntax = "proto3";

package intent;

// perbaiki jadi begini ⬇️
option go_package = "./proto/intent;intent";

service IntentService {
  rpc DetectIntent(IntentRequest) returns (IntentResponse) {}
}

message MessagePart {
  string type = 1;
  string text = 2;
}

message Message {
  string role = 1;
  string content = 2;
  repeated MessagePart parts = 3;
}

message IntentRequest {
  string id = 1;
  repeated Message messages = 2;
  string userId = 3;
  string sessionId = 4;
  string userEmail = 5;
}

message IntentMeta {
  string route = 1;
  string aggregation = 2;
  string granularity = 3;
  repeated string dimensions = 4;
  repeated string metrics = 5;
  repeated string entities = 6;
}

message Intent {
  string label = 1;
  double confidence = 2;
  IntentMeta meta = 3;
}

message IntentResponse {
  Intent intent = 1;
}
