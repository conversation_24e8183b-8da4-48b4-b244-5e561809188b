package grpcclient

import (
	"context"
	"time"

	intent "fin_gateway/proto"

	"google.golang.org/grpc"
)

type IntentClient struct {
	conn   *grpc.ClientConn
	client intent.IntentServiceClient
}

func New(addr string) (*IntentClient, error) {
	conn, err := grpc.Dial(addr, grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	return &IntentClient{
		conn:   conn,
		client: intent.NewIntentServiceClient(conn),
	}, nil
}

func (c *IntentClient) DetectIntent(id, sessionID, userID, userEmail, text string) (*intent.IntentResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// Create message according to fin_work_intent format
	message := &intent.Message{
		Role:    "user",
		Content: text,
	}

	return c.client.DetectIntent(ctx, &intent.IntentRequest{
		Id:        id,
		Messages:  []*intent.Message{message},
		UserId:    userID,
		SessionId: sessionID,
		UserEmail: userEmail,
	})
}

func (c *IntentClient) Close() {
	c.conn.Close()
}
