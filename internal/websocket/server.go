package websocket

import (
	"encoding/json"
	"net/http"
	"sync"

	"fin_gateway/internal/grpcclient"
	"fin_gateway/internal/rabbitmq"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type Server struct {
	upgrader   websocket.Upgrader
	clients    map[string]*websocket.Conn
	mu         sync.Mutex
	logger     *zap.Logger
	publisher  *rabbitmq.Publisher
	intentGrpc *grpcclient.IntentClient
}

func NewServer(logger *zap.Logger, pub *rabbitmq.Publisher, ic *grpcclient.IntentClient) *Server {
	return &Server{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		clients:    make(map[string]*websocket.Conn),
		logger:     logger,
		publisher:  pub,
		intentGrpc: ic,
	}
}

type IncomingMsg struct {
	ID        string `json:"id"`
	SessionID string `json:"session_id"`
	UserID    string `json:"user_id"`
	UserEmail string `json:"user_email"`
	Text      string `json:"text"`
}

func (s *Server) HandleWS(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.logger.Error("ws upgrade failed", zap.Error(err))
		return
	}
	defer conn.Close()

	for {
		_, msg, err := conn.ReadMessage()
		if err != nil {
			s.logger.Error("read ws msg failed", zap.Error(err))
			break
		}

		var in IncomingMsg
		if err := json.Unmarshal(msg, &in); err != nil {
			s.logger.Error("invalid msg", zap.Error(err))
			continue
		}

		// gRPC call to detect intent
		intentResp, err := s.intentGrpc.DetectIntent(in.ID, in.SessionID, in.UserID, in.UserEmail, in.Text)
		if err != nil {
			s.logger.Error("grpc detect failed", zap.Error(err))
			continue
		}

		// Extract intent data according to fin_work_intent format
		var intentLabel string
		var confidence float64
		var meta map[string]interface{}

		if intentResp.Intent != nil {
			intentLabel = intentResp.Intent.Label
			confidence = intentResp.Intent.Confidence
			if intentResp.Intent.Meta != nil {
				meta = map[string]interface{}{
					"route":       intentResp.Intent.Meta.Route,
					"aggregation": intentResp.Intent.Meta.Aggregation,
					"granularity": intentResp.Intent.Meta.Granularity,
					"dimensions":  intentResp.Intent.Meta.Dimensions,
					"metrics":     intentResp.Intent.Meta.Metrics,
					"entities":    intentResp.Intent.Meta.Entities,
				}
			}
		}

		out, _ := json.Marshal(map[string]interface{}{
			"id":         in.ID,
			"session_id": in.SessionID,
			"user_id":    in.UserID,
			"user_email": in.UserEmail,
			"text":       in.Text,
			"intent":     intentLabel,
			"confidence": confidence,
			"meta":       meta,
		})

		// Publish to RabbitMQ
		if err := s.publisher.Publish("chat.input", out); err != nil {
			s.logger.Error("publish failed", zap.Error(err))
		}
	}
}
